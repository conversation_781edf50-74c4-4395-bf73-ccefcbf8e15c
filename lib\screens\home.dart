import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:ddone/components/button/network_indicator.dart';
import 'package:ddone/components/chat_category/chat_category.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/chat_recent/chat_recent_cubit.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/delete_bookmark/delete_bookmark_cubit.dart';
import 'package:ddone/cubit/messaging/receive_message_cubit.dart';
import 'package:ddone/cubit/update/chat_ui_cubit.dart';
import 'package:ddone/cubit/update/group_ui_cubit.dart';
import 'package:ddone/cubit/update/info_cubit.dart';
import 'package:ddone/cubit/version_management/version_management_cubit.dart';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/cubit/xmpp/connection/xmpp_connection_cubit.dart';
import 'package:ddone/events/app_state_event.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/screens/chat.dart';
import 'package:ddone/screens/chat_recent.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/constants/value_constants.dart';
import 'package:ddone/environments/env.dart';
import 'package:ddone/cubit/auth/auth_cubit.dart';
import 'package:ddone/cubit/common/network/network_cubit.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/cubit/invitation/invitation_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/messaging/messages_cubit.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/callkit_service.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/services/elegant_notification_service.dart';
import 'package:ddone/services/fetch_xml_service.dart';
import 'package:ddone/services/firebase_service.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/services/notification_service.dart';
import 'package:ddone/services/upgrade_version_service.dart';
import 'package:ddone/services/voip_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/notification_util.dart';
import 'package:ddone/utils/page_view_util.dart';
import 'package:ddone/utils/permission_util.dart';
import 'package:ddone/utils/pop_out_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:ddone/utils/voip/incoming_call.dart';
import 'package:ddone/utils/voip/outgoing_call.dart';
import 'package:ddone/utils/orientation_util.dart';
import 'package:event_bus_plus/event_bus_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:logger/logger.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:ddone/cubit/members_presence/members_presence_cubit.dart';
import 'package:moxxmpp/moxxmpp.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:window_manager/window_manager.dart';
import 'package:tray_manager/tray_manager.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:windows_notification/notification_message.dart';
import 'package:windows_notification/windows_notification.dart';

class Home extends StatefulWidget {
  static const routeName = '/home';

  const Home({super.key});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home>
    with AutomaticKeepAliveClientMixin<Home>, WindowListener, TrayListener, WidgetsBindingObserver, PrefsAware {
  @override
  bool get wantKeepAlive => true;

  late final String? sipProxy;
  late final String? sipDomain;
  late final String? sipNumber;
  late final String? sipName;
  late final String? sipPwd;

  Menu? _menu;

  late StreamSubscription connectivitySubscription;

  late AuthCubit _authCubit;
  late HomeCubit _homeCubit;
  late VoipCubit _voipCubit;
  late InvitationCubit _invitationCubit;
  late MessagesCubit _messagesCubit;
  late MamListCubit _mamListCubit;
  late BookmarkListCubit _bookmarkListCubit;
  late PresencesCubit _presencesCubit;
  late ChatRecentCubit _chatRecentCubit;
  late ContactsCubit _contactsCubit;
  late LoginCubit _loginCubit;
  late ChatUiCubit _chatUiCubit;
  late GroupUiCubit _groupUiCubit;
  late DeleteBookmarkCubit _deleteBookmarkCubit;
  late InfoCubit _infoCubit;
  late ReceiveMessageCubit _receiveMessageCubit;
  late XmppConnectionCubit _xmppConnectionCubit;
  late VersionManagementCubit _versionManagementCubit;

  late NotificationService _notificationService;
  late FirebaseService _firebaseService;
  late VoipService _voipService;
  late CallkitService _callkitService;
  late WindowsNotification _windowsNotification;
  late Logger log;
  late IEventBus _eventBus;

  Widget buildPageView() {
    return PageView(
      physics: const NeverScrollableScrollPhysics(),
      controller: _homeCubit.pageController,
      onPageChanged: (index) {
        pageChanged(index);
        _homeCubit.jumpToPage(index);
      },
      children: homePageViewDefinition.map((d) => d.widget).toList(),
    );
  }

  void pageChanged(int index) {
    _homeCubit.updateSelectedIndex(index);
  }

  @override
  void onWindowFocus() async {
    log.t('home onWindowFocus _voipService lastVoipState ${_voipCubit.state}');
    _voipService.maintainConnection();
  }

  @override
  void onWindowBlur() {}

  void handleLocalNotificationAction(NotificationResponse payload) {
    log.t('handleLocalNotificationAction - payload: $payload');
    log.t('handleLocalNotificationAction - payload.notificationResponseType: ${payload.notificationResponseType}');
    log.t('handleLocalNotificationAction - payload.payload: ${payload.payload}');
    log.t('handleLocalNotificationAction - payload.actionId: ${payload.actionId}');
    log.t('handleLocalNotificationAction - payload.input: ${payload.input}');
    log.t('handleLocalNotificationAction - payload.id: ${payload.id}');

    switch (payload.actionId) {
      case notificationAcceptCallAction:
        if (_voipCubit.state is VoipSipIncomingCall) {
          _homeCubit.acceptCall();
        }

        break;
      case notificationDeclineCallAction:
        if (_voipCubit.state is VoipSipIncomingCall) {
          _homeCubit.declineCall();
        }

        break;
      default:
        // Handle other actions or payloads
        break;
    }

    if (payload.payload != null) {
      var jsonPayload = json.decode(payload.payload!);

      if (jsonPayload['routeName'] != null) {
        switch (jsonPayload['routeName']) {
          case RecentChat.routeName:
            _homeCubit.jumpToPageWithName(PageViewNameEnum.recentChat);
            _chatRecentCubit.setPageIndex(kRecentChatsIndex);
            if (jsonPayload['isGroup']) {
              _infoCubit.getGroupChatHistory(
                receiver: jsonPayload['jid'].split('/').first,
                nick: jsonPayload['jid'].split('/').first,
                name: jsonPayload['jid'].split('/').first,
                loginCubit: _loginCubit,
                mamListCubit: _mamListCubit,
              );
            } else {
              _infoCubit.getChatHistory(
                receiver: jsonPayload['jid'],
                loginCubit: _loginCubit,
                mamListCubit: _mamListCubit,
              );
            }
            if (isMobile && !_notificationService.isChatActive(jsonPayload['jid'])) {
              pushNamed(ChatPage.routeName);
            }
            break;
        }
      }
    }
  }

  void handleWindowsNotificationAction(NotificationCallBackDetails details) {
    final payload = details.message.payload;
    final type = payload['type'];

    switch (type) {
      case 'incoming_call':
        if (details.argrument.toString() == 'action:callAnswer') {
          if (_voipCubit.state is VoipSipIncomingCall) {
            _homeCubit.acceptCall();
          }
        } else if (details.argrument.toString() == 'action:callDecline') {
          if (_voipCubit.state is VoipSipIncomingCall) {
            _homeCubit.declineCall();
          }
        }

        break;
      case 'incoming_message':
        _homeCubit.jumpToPageWithName(PageViewNameEnum.recentChat);

        if (payload['isGroup']) {
          _infoCubit.getGroupChatHistory(
            receiver: payload['jid'].split('/').first,
            nick: payload['jid'].split('/').first,
            name: payload['jid'].split('/').first,
            loginCubit: _loginCubit,
            mamListCubit: _mamListCubit,
          );
        } else {
          _infoCubit.getChatHistory(
            receiver: payload['jid'],
            loginCubit: _loginCubit,
            mamListCubit: _mamListCubit,
          );
        }
        break;
      default:
    }
  }

  /// TO ASK: only use in loadAndShowDetails? so can ignore??
  void showVersionAlertDialog(Map<String, String> details) {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Update!'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('New version of ${env!.appName} is available: ${details['version']}'),
              const SizedBox(height: 4),
              Text('Now your version is ${env!.appVersion}. Please update!'),
              const SizedBox(height: 8),
              const Text('Release Notes:'),
              const SizedBox(height: 2),
              Text('${details['description']}'),
              const SizedBox(height: 8),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                final url = details['downloadPath']!;
                // const url = 'ms-windows-store://pdp/?productid=9P54KFGD0D0W';

                launchUrl(Uri.parse(url));

                pop();
              },
              child: const Text('Update Now'),
            ),
          ],
        );
      },
    );
  }

  /// TO ASK: Intention of this thing???
  void loadAndShowDetails() async {
    try {
      // Fetch XML details
      final details = await fetchAndParseXml('https://purptest.github.io/update_version/appcast.xml');

      // Show details in dialog
      details!['version'] != env!.appVersion ? showVersionAlertDialog(details) : null;
    } catch (e) {
      // Show error dialog
      if (!mounted) return;
      showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: const Text('Error'),
            content: Text(e.toString()),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ],
          );
        },
      );
    }
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this);
    final initialLifecycleState = WidgetsBinding.instance.lifecycleState;
    if (initialLifecycleState == AppLifecycleState.resumed) {
      prefs.setBool(CacheKeys.appOpen, true);
    } else {
      prefs.setBool(CacheKeys.appOpen, false);
    }

    _authCubit = BlocProvider.of<AuthCubit>(context);
    _homeCubit = BlocProvider.of<HomeCubit>(context);
    _voipCubit = BlocProvider.of<VoipCubit>(context);
    _invitationCubit = BlocProvider.of<InvitationCubit>(context);
    _messagesCubit = BlocProvider.of<MessagesCubit>(context);
    _mamListCubit = BlocProvider.of<MamListCubit>(context);
    _bookmarkListCubit = BlocProvider.of<BookmarkListCubit>(context);
    _presencesCubit = BlocProvider.of<PresencesCubit>(context);
    _chatRecentCubit = BlocProvider.of<ChatRecentCubit>(context);
    _contactsCubit = BlocProvider.of<ContactsCubit>(context);
    _loginCubit = BlocProvider.of<LoginCubit>(context);
    _chatUiCubit = BlocProvider.of<ChatUiCubit>(context);
    _groupUiCubit = BlocProvider.of<GroupUiCubit>(context);
    _deleteBookmarkCubit = BlocProvider.of<DeleteBookmarkCubit>(context);
    _infoCubit = BlocProvider.of<InfoCubit>(context);
    _receiveMessageCubit = BlocProvider.of<ReceiveMessageCubit>(context);
    _xmppConnectionCubit = BlocProvider.of<XmppConnectionCubit>(context);
    _versionManagementCubit = BlocProvider.of<VersionManagementCubit>(context);

    _notificationService = sl.get<NotificationService>();
    _firebaseService = sl.get<FirebaseService>();
    _voipService = sl.get<VoipService>();
    _callkitService = sl.get<CallkitService>();
    log = sl.get<Logger>();
    _eventBus = sl.get<IEventBus>();

    _notificationService.initializeLocalNotifications(
      onSelectNotification: handleLocalNotificationAction,
      // onBackgroundSelectNotification: handleNotificationAndroidAction,
    );

    // _notificationService.requestNotificationPermission();

    if (isWindows) {
      _windowsNotification = sl.get<WindowsNotification>();
      _windowsNotification.initNotificationCallBack(handleWindowsNotificationAction);
    } else if (isMobile) {
      _callkitService.init(
          incomingCallCallback: () {
            if (isAndroid) {
              // foreground service will start and take over, so dispose the session in main isolate
              // such that we don't have call conflict.
              // - if got 2 session, when one of the session accepted call, another will receive hangup.
              //   This will cause the 1st call to hangup too because of callkit's endCall as we only
              //   support 1 call at a time now.
              _voipService.disposeJanus();
            } else {
              _voipService.maintainConnection();
            }
          },
          acceptedCallCallback: (callDirection) {
            if (callDirection != CallDirection.outgoing) {
              log.t('in foreground service accept');
              _callkitAcceptCall();
            }
          },
          declinedCallCallback: (callDirection) {
            if (callDirection != CallDirection.outgoing) {
              log.t('in foreground service decline');
              _callkitDeclineCall();
            }
          },
          endedCallCallback: () {
            _callkitHangupCall();
          },
          missedCallCallback: () {
            _callkitMissedCall();
          },
          iosToggleHoldCallCallback: () {
            _callkitIosHold();
          },
          iosToggleMuteCallCallback: (isMuted) {
            _callkitIosToggleMic(isMuted);
          },
          iosToggleAudioSessionCallback: (isActivate) {});
      _firebaseService.init(
        onMessageOpenedApp: _handleMessageOnAppOpen,
        onMessage: _firebaseService.handleMessage,
        onBackgroundMessage: firebaseMessagingBackgroundHandler,
      );
    }

    if (isDesktop) {
      windowManager.addListener(this);
      _initWindowManager();

      trayManager.addListener(this);
      setTrayIcon();
    }

    if (isMobile) {
      _homeCubit.updateSelectedIndex(2);
    }

    initVoip();

    _homeCubit.checkForMissCall();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Set device orientations based on device type
      OrientationUtil.setDeviceOrientations(context);

      _versionManagementCubit.checkForUpdates(
        screenWidth: '${MediaQuery.of(context).size.width}',
        screenHeight: '${MediaQuery.of(context).size.height}',
        pixelRatio: '${MediaQuery.of(context).devicePixelRatio}',
      );

      // In android, call get accepted via background isolate and connection get established in
      // foreground isoalte, so main isolate when start up need to set this when there is active call.
      _callkitService.setActiveCallUuid();

      // grab the current state from foreground service.
      _voipService.retrieveForegroundState();

      // check whether we need to display the loading accepted call screen during the period where user
      // already accepted call but foreground service still need time to start to actually accept the call.
      _voipCubit.checkAcceptingCallFromBackground();

      _requestInitialPermissions();

      // Request full screen intent permission for Android 14+
      // _callkitService.requestCallKitFullIntentPermission(context);

      // check whether we need to display the accepted call screen when app is resumed.
      _checkAndShowAcceptedCallDialog();

      // Request App Tracking Transparency permission for iOS only
      // Not using now, but future may need it so keep it here first.
      // AppTrackingTransparencyService.initializeTracking(context);
    });
  }

  Future<void> _requestInitialPermissions() async {
    await PermissionUtil.handleCustomBoolPermissionRequest(
      () => _firebaseService.requestNotificationPermissions(),
      context,
      title: 'Notification Permission Required',
      message: 'Notifications is essential for recieving calls and messages when app is terminated.',
    );
    if (isMobile && mounted) {
      await PermissionUtil.handlePermissionRequest(Permission.microphone, context);
    }
    if (isAndroid && mounted) {
      await PermissionUtil.handlePermissionRequest(Permission.phone, context);
    }
    if (isAndroid && mounted) {
      await _callkitService.requestCallKitFullIntentPermission(context);
    }
    if (mounted) {
      await PermissionUtil.handleCustomBoolPermissionRequest(
        () => _notificationService.requestNotificationPermission(),
        context,
        title: 'Notification Permission Required',
        message: 'Notifications is essential for recieving calls and messages when app is terminated.',
      );
    }
  }

  Future<void> initVoip() async {
    await _voipCubit.remoteVideoRenderer.initialize();
    _voipService.initJanus();
  }

  Future<void> _callkitAcceptCall() async {
    await _voipService.acceptCall();
  }

  Future<void> _callkitDeclineCall() async {
    await _voipService.declineCall();
  }

  Future<void> _callkitHangupCall() async {
    await _voipService.hangupCall();
  }

  Future<void> _callkitMissedCall() async {
    if (_voipCubit.state is VoipSipIncomingCall) return;
    _voipService.disposeJanus();
  }

  Future<void> _callkitIosToggleMic(bool isMuted) async {
    _voipService.iosCallToggleMic(isMuted);
  }

  Future<void> _callkitIosHold() async {
    _voipService.holdCall();
  }

  @override
  void didChangeDependencies() async {
    super.didChangeDependencies();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    log.t('home didChangeAppLifecycleState state:$state');
    _eventBus.fire(AppStateEvent(state));
    if (state == AppLifecycleState.resumed) {
      prefs.setBool(CacheKeys.appOpen, true);
      _voipService.maintainConnection();
      _checkAndShowAcceptedCallDialog();
    } else if (state == AppLifecycleState.paused) {
      prefs.setBool(CacheKeys.appOpen, false);
      _voipService.disposeJanusWhenIsMobileAndNoCalls();
    }
  }

  /// Check if we need to show AcceptedCallDialog when app resumes
  void _checkAndShowAcceptedCallDialog() async {
    // Wait a bit to ensure the app is fully resumed and context is available
    await Future.delayed(const Duration(milliseconds: 500));

    // Check if the current VoIP state is VoipSipAccepted
    if (_voipCubit.state is VoipSipAccepted && mounted) {
      final voipState = _voipCubit.state as VoipSipAccepted;
      log.t('App resumed with VoipSipAccepted state - checking if AcceptedCallDialog should be shown');

      // Check if there's already a dialog showing by checking the modal route
      final modalRoute = ModalRoute.of(context);
      final hasModalDialog = modalRoute != null && modalRoute.isCurrent && modalRoute.settings.name != '/';

      if (!hasModalDialog) {
        log.t('No modal dialog detected - showing AcceptedCallDialog');

        // Start the stopwatch for call duration tracking
        _homeCubit.startStopWatch();

        // Show the accepted call dialog
        if (mounted) {
          showAcceptedCallDialog(
            context,
            homeCubit: _homeCubit,
            caller: voipState.caller ?? voipState.callee ?? '',
            callerID: voipState.callerId ?? voipState.calleeId ?? '',
          );
        }
      } else {
        log.t('Modal dialog already showing - skipping AcceptedCallDialog');
      }
    }
  }

  @override
  void dispose() {
    connectivitySubscription.cancel();
    windowManager.removeListener(this);
    trayManager.removeListener(this);

    prefs.setBool(CacheKeys.appOpen, false);
    WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }

  void _handleMessageOnAppOpen(RemoteMessage message) {
    if (message.data['type'] == 'chat') {}
    debugPrint('_handleMessage: ${message.data}');
    final RemoteNotification? notification = message.notification;
    if (notification != null) {
      debugPrint('******** Received a message on app open! ********');
      debugPrint('******** Notification Title: ${notification.title} ********');
      debugPrint('******** Notification Body: ${notification.body} ********');
    }
  }

  void _initWindowManager() async {
    await windowManager.setPreventClose(true);
  }

  void setTrayIcon() async {
    await trayManager.setIcon(isDesktop ? '$imagePathPrefix/app_icon.ico' : '$imagePathPrefix/dot_dash_logo.png');

    _menu ??= Menu(items: [
      MenuItem(
        label: 'Show App',
        onClick: (menuItem) {
          windowManager.show();
        },
      ),
      MenuItem(
        label: 'Exit',
        onClick: (menuItem) async {
          trayManager.destroy();

          windowManager.destroy();

          if (_loginCubit.state is LoginAuthenticated) {
            final loginAuthenticatedState = _loginCubit.state as LoginAuthenticated;
            final presenceUnsubscribe =
                loginAuthenticatedState.connection.getManagerById<PresenceManager>(presenceManager)!;
            await presenceUnsubscribe.sendUnavailablePresence();

            loginAuthenticatedState.connection.disconnect();
          }
        },
      )
    ]);

    await trayManager.setContextMenu(_menu!);
  }

  @override
  void onTrayIconMouseDown() {
    windowManager.show();
  }

  @override
  void onTrayIconRightMouseDown() {
    trayManager.popUpContextMenu();
  }

  @override
  void onWindowClose() async {
    showConfirmDialog(
      context: context,
      title: 'Do you want to exit?',
      rightButtonText: 'Minimize',
      onPressedRightButton: windowManager.hide,
      leftButtonText: 'Exit',
      onPressedLeftButton: () async {
        trayManager.destroy();

        windowManager.destroy();

        if (_loginCubit.state is LoginAuthenticated) {
          final loginAuthenticatedState = _loginCubit.state as LoginAuthenticated;
          final presenceUnsubscribe =
              loginAuthenticatedState.connection.getManagerById<PresenceManager>(presenceManager)!;
          await presenceUnsubscribe.sendUnavailablePresence();

          loginAuthenticatedState.connection.disconnect();
        }
      },
    );
  }

  void _showVersionUpdateDialog(BuildContext context,
      {required bool forceUpdate,
      required String currentVersion,
      required String latestVersion,
      required String updateUrl}) {
    showDialog(
      context: context,
      barrierDismissible: false, // User must interact with the dialog
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Update Available!'),
          content: Text(
            forceUpdate
                ? 'A compulsory update is available. Please update to continue using the app.\nCurrent version: $currentVersion\nLatest version: $latestVersion'
                : 'A new version is available. Would you like to update?\nCurrent version: $currentVersion\nLatest version: $latestVersion',
          ),
          actions: <Widget>[
            if (!forceUpdate)
              TextButton(
                child: const Text('Later'),
                onPressed: () {
                  pop();
                },
              ),
            TextButton(
              child: const Text('Update Now'),
              onPressed: () {
                _launchStoreUrl(updateUrl);
                if (!forceUpdate) pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _showErrorDialog(BuildContext context, String errorMessage) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Version Check Failed'),
          content: Text('An error occurred: $errorMessage'),
          actions: <Widget>[
            TextButton(
              child: const Text('OK'),
              onPressed: () {
                pop();
              },
            ),
          ],
        );
      },
    );
  }

  Future<void> _launchStoreUrl(String storeUrl) async {
    final Uri url = Uri.parse(storeUrl);
    if (await canLaunchUrl(url)) {
      bool launched = await launchUrl(url, mode: LaunchMode.externalApplication);
      if (!launched) {
        log.d('Could not launch $url in external app');
      }
    } else {
      log.e('Cannot launch $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return MultiBlocListener(
      listeners: [
        BlocListener<ReceiveMessageCubit, ReceiveMessageState>(listener: (context, receveMsgState) {
          if (receveMsgState is ReceiveMessageUpdated) {
            if (Platform.isWindows) {
              ElegantNotificationService elegantNotificationService = ElegantNotificationService();

              elegantNotificationService.showStackNotification(
                context,
                title: receveMsgState.name,
                desc: receveMsgState.message,
                gestureTapCallback: () {
                  _homeCubit.jumpToPageWithName(PageViewNameEnum.recentChat);

                  if (receveMsgState.isGroup) {
                    _infoCubit.getGroupChatHistory(
                      receiver: receveMsgState.jid.split('/').first,
                      nick: receveMsgState.jid.split('/').first,
                      name: receveMsgState.jid.split('/').first,
                      loginCubit: _loginCubit,
                      mamListCubit: _mamListCubit,
                    );
                  } else {
                    _infoCubit.getChatHistory(
                      receiver: receveMsgState.jid,
                      loginCubit: _loginCubit,
                      mamListCubit: _mamListCubit,
                    );
                  }
                },
              );
            }
          }
        }),
        BlocListener<NetworkCubit, NetworkState>(
          listener: (context, networkState) {
            if (networkState is NetworkConnected) {
              _authCubit.autoLogin(
                homeCubit: _homeCubit,
                voipCubit: _voipCubit,
                authCubit: _authCubit,
                contactsCubit: _contactsCubit,
                loginCubit: _loginCubit,
                invitationCubit: _invitationCubit,
                messagesCubit: _messagesCubit,
                mamListCubit: _mamListCubit,
                bookmarkListCubit: _bookmarkListCubit,
                presencesCubit: _presencesCubit,
                chatRecentCubit: _chatRecentCubit,
                chatUiCubit: _chatUiCubit,
                groupUiCubit: _groupUiCubit,
                deleteBookmarkCubit: _deleteBookmarkCubit,
                receiveMessageCubit: _receiveMessageCubit,
                xmppConnectionCubit: _xmppConnectionCubit,
                versionManagementCubit: _versionManagementCubit,
              );
            } else if (networkState is NetworkReconnected) {
              showSnackBarWithText(
                context,
                'Connected to network',
              );
              _authCubit.autoLogin(
                homeCubit: _homeCubit,
                voipCubit: _voipCubit,
                authCubit: _authCubit,
                contactsCubit: _contactsCubit,
                loginCubit: _loginCubit,
                invitationCubit: _invitationCubit,
                messagesCubit: _messagesCubit,
                mamListCubit: _mamListCubit,
                bookmarkListCubit: _bookmarkListCubit,
                presencesCubit: _presencesCubit,
                chatRecentCubit: _chatRecentCubit,
                chatUiCubit: _chatUiCubit,
                groupUiCubit: _groupUiCubit,
                deleteBookmarkCubit: _deleteBookmarkCubit,
                receiveMessageCubit: _receiveMessageCubit,
                xmppConnectionCubit: _xmppConnectionCubit,
                versionManagementCubit: _versionManagementCubit,
              );
            } else if (networkState is NetworkDisconnected) {
              showSnackBarWithText(
                context,
                'Disconnected to network',
              );
            }
          },
        ),
        BlocListener<VoipCubit, VoipState>(
          listener: (context, voipState) {
            if (voipState is VoipSipIncomingCall) {
              // we use callkit to accept call, so no need to display incoming call dialog.
              if (isMobile) return;

              _homeCubit.playIncomingRingtone();

              if (isWindows) {
                windowManager.show();
              } else {
                _notificationService.showLocalNotification(
                  title: voipState.callerId!,
                  desc: 'Incoming Call',
                  payload: '',
                  // androidNotificaitonAction: androidIncomingCallTemplate,
                  // darwinNotificationDetails: iOSMacosIncomingCallTemplete,
                  windowNotificationMessage: windowsNotificationMessage(
                    id: 'notificationid_1',
                    group: 'incoming_call_group',
                    launch: 'callLaunchArg',
                    payload: {'type': 'incoming_call'},
                  ),
                  windowsNotificationTemplate: twoButtonsNotificationTemplateForWindows(
                    title: voipState.callerId!,
                    desc: 'Incoming Call',
                    leftButtonText: 'Decline',
                    rightButtonText: 'Answer',
                    leftButtonArg: 'callDecline',
                    rightButtonArg: 'callAnswer',
                    scenario: 'incomingCall',
                    launch: 'callLaunchArg',
                  ),
                );
              }

              showDialog(
                barrierDismissible: false,
                context: context,
                builder: (BuildContext context) {
                  return IncomingCallDialog(
                    caller: voipState.caller,
                    callerID: voipState.callerId,
                    onAccept: () {
                      _homeCubit.acceptCall();
                    },
                    onDecline: () => _homeCubit.declineCall(),
                  );
                },
              );
            } else if (voipState is VoipSipCalling) {
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) {
                  return const OutGoingCallDialog();
                },
              );
            } else if (voipState is VoipSipAccepted) {
              // Handle accepted call state directly - this allows the app to show
              // the accepted call screen even when starting directly in VoipSipAccepted state
              // Use a small delay to ensure any existing dialogs are properly closed
              // before showing the accepted call dialog
              Future.delayed(const Duration(milliseconds: 200), () {
                if (context.mounted) {
                  // Check if there's already a dialog showing to prevent duplicates
                  final modalRoute = ModalRoute.of(context);
                  final hasModalDialog = modalRoute != null && modalRoute.isCurrent && modalRoute.settings.name != '/';

                  if (!hasModalDialog) {
                    // Start the stopwatch for call duration tracking
                    _homeCubit.startStopWatch();
                    showAcceptedCallDialog(
                      context,
                      homeCubit: _homeCubit,
                      caller: voipState.caller ?? voipState.callee ?? '',
                      callerID: voipState.callerId ?? voipState.calleeId ?? '',
                    );
                  }
                }
              });
            } else if (voipState is VoipSipHangup) {
              _homeCubit.stopAndResetRingtonePlayer();
            }
          },
        ),
        BlocListener<AuthCubit, AuthState>(
          listener: (context, authState) {
            if (authState is AuthAutoLoginFail) {
              EasyLoadingService().showErrorWithText('Please log in again');
            }
          },
        ),
        BlocListener<VersionManagementCubit, VersionManagementState>(
          listener: (context, versionState) {
            if (versionState is VersionOutdated) {
              _showVersionUpdateDialog(
                context,
                forceUpdate: versionState.forceUpdate,
                currentVersion: versionState.curerntVersion,
                latestVersion: versionState.latestVersion,
                updateUrl: versionState.updateUrl,
              );
            } else if (versionState is VersionCheckFailed) {
              _showErrorDialog(context, versionState.error);
            }
          },
        ),
      ],
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, themeState) {
          final colorTheme = themeState.colorTheme;
          final textTheme = themeState.themeData.textTheme;

          // Fetch Microsoft Store version asynchronously without blocking UI
          const microsoftStoreUrl = 'https://www.microsoft.com/store/r/9P54KFGD0D0W';
          UpgrateVersionService().fetchMicrosoftStoreVersion(microsoftStoreUrl).then((latestVersion) {
            log.d('Latest Version Microsoft : $latestVersion');
          }).catchError((error) {
            log.w('Failed to fetch Microsoft Store version: $error');
          });

          return SafeArea(
            child: Scaffold(
              backgroundColor: colorTheme.backgroundColor,
              body: isDesktop
                  ? Row(
                      children: <Widget>[
                        BlocBuilder<HomeCubit, HomeState>(
                          builder: (context, homeState) {
                            return NavigationRail(
                              backgroundColor: Colors.grey.shade800,
                              selectedIndex: homeState.navSelectedIndex,
                              groupAlignment: 0.0,
                              unselectedIconTheme:
                                  IconThemeData(color: colorTheme.onPrimaryColor.withOpacity(opacityHigh)),
                              unselectedLabelTextStyle: textTheme.bodyMedium!.copyWith(
                                color: colorTheme.onPrimaryColor.withOpacity(opacityHigh),
                              ),
                              selectedLabelTextStyle: textTheme.bodyMedium!.copyWith(color: colorTheme.primaryColor),
                              onDestinationSelected: (int index) {
                                _homeCubit.bottomTapped(index);
                              },
                              labelType: NavigationRailLabelType.all,
                              leading: Column(
                                children: [
                                  const NetworkIndicatorButton(),
                                  SizedBox(
                                    height: context.deviceHeight(0.17),
                                  )
                                ],
                              ),
                              trailing: Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.fromLTRB(0.0, 0.0, 0.0, 8.0),
                                  child: Align(
                                      alignment: Alignment.bottomCenter,
                                      child: InkWell(
                                        splashFactory: NoSplash.splashFactory,
                                        onTap: () async {
                                          final Uri url = Uri.parse(env!.contactUsUrl);

                                          if (!await launchUrl(url)) {
                                            throw Exception('Could not launch $url');
                                          }
                                        },
                                        child: Text(
                                          'Support',
                                          style: textTheme.bodyMedium!.copyWith(color: colorTheme.primaryColor),
                                        ),
                                      )),
                                ),
                              ),
                              destinations: const [
                                NavigationRailDestination(
                                  icon: Icon(Icons.history),
                                  label: Text('History'),
                                ),
                                NavigationRailDestination(
                                  icon: Icon(Icons.dialpad_outlined),
                                  label: Text('Main'),
                                ),
                                NavigationRailDestination(
                                  icon: Icon(Icons.chat),
                                  label: Text('Chat'),
                                ),
                                NavigationRailDestination(
                                  icon: Icon(Icons.groups),
                                  label: Text('DDMeet'),
                                ),
                                NavigationRailDestination(
                                  icon: Icon(Icons.person),
                                  label: Text('Me'),
                                ),
                              ],
                            );
                          },
                        ),
                        Expanded(
                          child: buildPageView(),
                        ),
                      ],
                    )
                  : buildPageView(),
              bottomNavigationBar: isDesktop
                  ? null
                  : BlocBuilder<HomeCubit, HomeState>(
                      builder: (context, homeState) {
                        return BottomNavigationBar(
                          type: BottomNavigationBarType.fixed,
                          iconSize: iconSizeLarge,
                          currentIndex: homeState.navSelectedIndex,
                          onTap: (index) {
                            _homeCubit.bottomTapped(index);
                          },
                          items: const [
                            BottomNavigationBarItem(
                              icon: Icon(Icons.history),
                              label: 'History',
                            ),
                            BottomNavigationBarItem(
                              icon: Icon(Icons.perm_contact_cal_sharp),
                              label: 'Contacts',
                            ),
                            BottomNavigationBarItem(
                              icon: Icon(Icons.dialpad_outlined),
                              label: 'Dial',
                            ),
                            BottomNavigationBarItem(
                              icon: Icon(Icons.chat),
                              label: 'Chat',
                            ),
                            BottomNavigationBarItem(
                              icon: Icon(Icons.groups),
                              label: 'DDMeet',
                            ),
                            BottomNavigationBarItem(
                              icon: Icon(Icons.person),
                              label: 'Me',
                            ),
                          ],
                          // unselectedItemColor: Colors.grey,
                          selectedItemColor: colorTheme.onBackgroundColor,
                          showUnselectedLabels: true,
                        );
                      },
                    ),
            ),
          );
        },
      ),
    );
  }
}

class BottomNavBarItem extends StatelessWidget {
  final IconData iconData;
  final String text;
  final bool isSelected;
  final GestureTapCallback gestureTapCallback;

  const BottomNavBarItem({
    required this.iconData,
    required this.text,
    required this.isSelected,
    required this.gestureTapCallback,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;

        return InkWell(
          onTap: gestureTapCallback,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                iconData,
                size: iconSizeLarge,
                color: isSelected ? colorTheme.onBackgroundColor : colorTheme.backgroundColor,
              ),
              Text(
                text,
                style: textTheme.labelLarge!
                    .copyWith(color: isSelected ? colorTheme.onBackgroundColor : colorTheme.backgroundColor),
              ),
            ],
          ),
        );
      },
    );
  }
}
